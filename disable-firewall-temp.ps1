# Temporarily disable Windows Firewall for Redis development
# WARNING: Only use this for development/testing environments

Write-Host "Temporarily disabling Windows Firewall..." -ForegroundColor Yellow
Write-Host "WARNING: This is only recommended for development environments!" -ForegroundColor Red

# Disable firewall for all profiles
Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False

Write-Host "✅ Windows Firewall disabled for all profiles" -ForegroundColor Green
Write-Host "Remember to re-enable it later with: Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True" -ForegroundColor Cyan

Read-Host "Press Enter to continue..."
