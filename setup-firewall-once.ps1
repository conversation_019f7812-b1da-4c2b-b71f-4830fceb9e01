# Redis Cluster Firewall Setup - Run Once
# This script will permanently add firewall rules for Redis cluster

Write-Host "Setting up permanent firewall rules for Redis Cluster..." -ForegroundColor Green

# Redis data ports
$dataPorts = @(7001, 7002, 7003, 8001, 8002, 8003)
# Redis cluster bus ports  
$busPorts = @(17001, 17002, 17003, 18001, 18002, 18003)

Write-Host "Adding rules for Redis data ports..." -ForegroundColor Yellow
foreach ($port in $dataPorts) {
    try {
        New-NetFirewallRule -DisplayName "Redis-Data-$port" -Direction Inbound -Protocol TCP -LocalPort $port -Action Allow -Profile Any -Enabled True
        Write-Host "✓ Added rule for data port $port" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Rule for port $port may already exist" -ForegroundColor Yellow
    }
}

Write-Host "Adding rules for Redis cluster bus ports..." -ForegroundColor Yellow
foreach ($port in $busPorts) {
    try {
        New-NetFirewallRule -DisplayName "Redis-Bus-$port" -Direction Inbound -Protocol TCP -LocalPort $port -Action Allow -Profile Any -Enabled True
        Write-Host "✓ Added rule for bus port $port" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Rule for port $port may already exist" -ForegroundColor Yellow
    }
}

# Add a general Redis range rule (alternative approach)
try {
    New-NetFirewallRule -DisplayName "Redis-Cluster-Range" -Direction Inbound -Protocol TCP -LocalPort 7000-8999,17000-18999 -Action Allow -Profile Any -Enabled True
    Write-Host "✓ Added Redis cluster port range rule" -ForegroundColor Green
} catch {
    Write-Host "⚠ Range rule may already exist" -ForegroundColor Yellow
}

Write-Host "`n✅ Firewall setup completed!" -ForegroundColor Green
Write-Host "These rules are now permanent and will persist after reboots." -ForegroundColor Cyan
Write-Host "You won't need to configure firewall again for Redis cluster." -ForegroundColor Cyan

Read-Host "Press Enter to continue..."
