@echo off
echo Stopping all Redis cluster nodes...

echo Connecting to each node and sending SH<PERSON><PERSON><PERSON><PERSON> command...

redis-cli.exe -p 7001 shutdown nosave 2>nul
redis-cli.exe -p 7002 shutdown nosave 2>nul
redis-cli.exe -p 7003 shutdown nosave 2>nul
redis-cli.exe -p 8001 shutdown nosave 2>nul
redis-cli.exe -p 8002 shutdown nosave 2>nul
redis-cli.exe -p 8003 shutdown nosave 2>nul

echo Waiting for processes to terminate...
timeout /t 3

echo Force killing any remaining redis-server processes...
taskkill /f /im redis-server.exe 2>nul

echo Cleaning up cluster configuration files...
del /q "c:\redis\redis-cluster\7001\nodes-*.conf" 2>nul
del /q "c:\redis\redis-cluster\7002\nodes-*.conf" 2>nul
del /q "c:\redis\redis-cluster\7003\nodes-*.conf" 2>nul
del /q "c:\redis\redis-cluster\8001\nodes-*.conf" 2>nul
del /q "c:\redis\redis-cluster\8002\nodes-*.conf" 2>nul
del /q "c:\redis\redis-cluster\8003\nodes-*.conf" 2>nul

echo All Redis nodes stopped and cluster configuration cleaned.
pause
