@echo off
echo Starting Redis Cluster with fixed configuration...

echo Stopping any existing Redis processes...
taskkill /f /im redis-server.exe 2>nul

echo Waiting for processes to stop...
timeout /t 3

echo Starting Redis nodes...

echo Starting node 7001...
cd /d "C:\redis\redis-cluster\7001"
start "Redis-7001" redis-server.exe redis.windows.conf

echo Starting node 7002...
cd /d "C:\redis\redis-cluster\7002"
start "Redis-7002" redis-server.exe redis.windows.conf

echo Starting node 7003...
cd /d "C:\redis\redis-cluster\7003"
start "Redis-7003" redis-server.exe redis.windows.conf

echo Starting node 8001...
cd /d "C:\redis\redis-cluster\8001"
start "Redis-8001" redis-server.exe redis.windows.conf

echo Starting node 8002...
cd /d "C:\redis\redis-cluster\8002"
start "Redis-8002" redis-server.exe redis.windows.conf

echo Starting node 8003...
cd /d "C:\redis\redis-cluster\8003"
start "Redis-8003" redis-server.exe redis.windows.conf

echo Waiting for nodes to initialize...
timeout /t 10

echo Testing node connections...
cd /d "C:\redis\redis-cluster"

echo Testing 7001...
redis-cli.exe -p 7001 ping

echo Testing 7002...
redis-cli.exe -p 7002 ping

echo Testing 7003...
redis-cli.exe -p 7003 ping

echo Testing 8001...
redis-cli.exe -p 8001 ping

echo Testing 8002...
redis-cli.exe -p 8002 ping

echo Testing 8003...
redis-cli.exe -p 8003 ping

echo.
echo All nodes should now be running!
echo You can check cluster status with:
echo redis-cli.exe -c -p 7001 cluster nodes
echo.
pause
