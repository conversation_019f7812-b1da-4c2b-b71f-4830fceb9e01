<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .game-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .game-board {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .info-panel {
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            min-width: 200px;
        }
        
        .score {
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .next-piece {
            margin-top: 20px;
        }
        
        .next-canvas {
            border: 2px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .controls {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .controls h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .game-over {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameCanvas" class="game-board" width="300" height="600"></canvas>
        <div class="info-panel">
            <div class="score">
                <div>分数: <span id="score">0</span></div>
                <div>等级: <span id="level">1</span></div>
                <div>行数: <span id="lines">0</span></div>
            </div>
            <div class="next-piece">
                <h3>下一个:</h3>
                <canvas id="nextCanvas" class="next-canvas" width="120" height="120"></canvas>
            </div>
            <div class="controls">
                <h3>控制:</h3>
                <div>← → 移动</div>
                <div>↓ 加速下降</div>
                <div>↑ 旋转</div>
                <div>空格 暂停</div>
            </div>
            <button onclick="restartGame()">重新开始</button>
        </div>
    </div>
    
    <div id="gameOver" class="game-over">
        <h2>游戏结束!</h2>
        <p>最终分数: <span id="finalScore">0</span></p>
        <button onclick="restartGame()">再来一局</button>
    </div>
    
    <script src="tetris.js"></script>
</body>
</html>
