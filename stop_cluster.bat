@echo off
set START_PORT=7001
set END_PORT=7006
 
echo Closing port  %START_PORT%-%END_PORT%...
echo.
 
for /l %%i in (%START_PORT%, 1, %END_PORT%) do (
    call :closeProgram %%i
)
 
echo.
echo Closing all
echo.
 
taskkill /f /fi "imagename eq cmd.exe"
 
echo.
echo All closed.
goto :eof
 
:closeProgram
set PORT=%1
 
echo Searching for programs using port %PORT%...
 
for /f "tokens=5" %%a in ('netstat -ano ^| findstr /i ":%PORT%"') do (
    taskkill /f /pid %%a
    echo Program with port %PORT% has been closed.
)
 
echo.
echo End of program list for port %PORT%.
goto :eof