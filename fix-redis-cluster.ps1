# Redis Cluster Fix Script for Windows
Write-Host "Redis Cluster Fix Script" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Step 1: Stop all Redis processes
Write-Host "Step 1: Stopping all Redis processes..." -ForegroundColor Yellow
try {
    Get-Process redis-server -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "All Redis processes stopped." -ForegroundColor Green
} catch {
    Write-Host "No Redis processes found or already stopped." -ForegroundColor Green
}

# Step 2: Clean up cluster configuration files
Write-Host "Step 2: Cleaning up cluster configuration files..." -ForegroundColor Yellow
$nodes = @("7001", "7002", "7003", "8001", "8002", "8003")
foreach ($node in $nodes) {
    $path = "c:\redis\redis-cluster\$node\nodes-*.conf"
    if (Test-Path $path) {
        Remove-Item $path -Force
        Write-Host "Cleaned cluster config for node $node" -ForegroundColor Green
    }
}

# Step 3: Add Windows Firewall rules
Write-Host "Step 3: Adding Windows Firewall rules..." -ForegroundColor Yellow
$ports = @(7001, 7002, 7003, 8001, 8002, 8003, 17001, 17002, 17003, 18001, 18002, 18003)
foreach ($port in $ports) {
    try {
        New-NetFirewallRule -DisplayName "Redis-$port" -Direction Inbound -Protocol TCP -LocalPort $port -Action Allow -ErrorAction SilentlyContinue
        Write-Host "Added firewall rule for port $port" -ForegroundColor Green
    } catch {
        Write-Host "Firewall rule for port $port already exists or failed to create" -ForegroundColor Yellow
    }
}

# Step 4: Start Redis nodes
Write-Host "Step 4: Starting Redis nodes..." -ForegroundColor Yellow
foreach ($node in $nodes) {
    $workingDir = "c:\redis\redis-cluster\$node"
    if (Test-Path $workingDir) {
        Start-Process -FilePath "redis-server.exe" -ArgumentList "redis.windows.conf" -WorkingDirectory $workingDir -WindowStyle Minimized
        Write-Host "Started Redis node $node" -ForegroundColor Green
        Start-Sleep -Seconds 2
    } else {
        Write-Host "Directory not found: $workingDir" -ForegroundColor Red
    }
}

# Step 5: Wait and verify nodes are running
Write-Host "Step 5: Waiting for nodes to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host "Step 6: Verifying nodes are running..." -ForegroundColor Yellow
foreach ($node in $nodes) {
    try {
        $result = & redis-cli.exe -p $node ping 2>$null
        if ($result -eq "PONG") {
            Write-Host "Node $node is running" -ForegroundColor Green
        } else {
            Write-Host "Node $node is NOT responding" -ForegroundColor Red
        }
    } catch {
        Write-Host "Node $node is NOT responding" -ForegroundColor Red
    }
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Verify all nodes are running with: redis-cli.exe -p 7001 ping" -ForegroundColor White
Write-Host "2. Create cluster with: redis-cli.exe --cluster create --cluster-replicas 1 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 127.0.0.1:8001 127.0.0.1:8002 127.0.0.1:8003" -ForegroundColor White
Write-Host "3. When prompted, type 'yes' to accept the configuration" -ForegroundColor White

Write-Host "`nScript completed!" -ForegroundColor Green
