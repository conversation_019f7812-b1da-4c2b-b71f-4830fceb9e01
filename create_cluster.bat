@echo off
echo Creating Redis Cluster...

echo Checking if all nodes are running...
echo Checking port 7001...
redis-cli.exe -p 7001 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 7001 is not running!
    pause
    exit /b 1
)

echo Checking port 7002...
redis-cli.exe -p 7002 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 7002 is not running!
    pause
    exit /b 1
)

echo Checking port 7003...
redis-cli.exe -p 7003 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 7003 is not running!
    pause
    exit /b 1
)

echo Checking port 8001...
redis-cli.exe -p 8001 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 8001 is not running!
    pause
    exit /b 1
)

echo Checking port 8002...
redis-cli.exe -p 8002 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 8002 is not running!
    pause
    exit /b 1
)

echo Checking port 8003...
redis-cli.exe -p 8003 ping
if %errorlevel% neq 0 (
    echo ERROR: Redis node 8003 is not running!
    pause
    exit /b 1
)

echo All nodes are running. Creating cluster...
echo.
echo This will create a cluster with 3 masters and 3 slaves.
echo Press any key to continue or Ctrl+C to cancel...
pause

redis-cli.exe --cluster create --cluster-replicas 1 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 127.0.0.1:8001 127.0.0.1:8002 127.0.0.1:8003

echo.
echo Cluster creation completed!
echo You can check cluster status with: redis-cli.exe -c -p 7001 cluster nodes
pause
