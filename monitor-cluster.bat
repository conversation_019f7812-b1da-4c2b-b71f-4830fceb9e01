@echo off
title Redis Cluster Monitor
color 0A

:MONITOR_LOOP
cls
echo ========================================
echo Redis Cluster Status Monitor
echo ========================================
echo Time: %date% %time%
echo.

echo [1] Cluster Nodes Status:
echo ----------------------------------------
redis-cli -c -p 7001 cluster nodes 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to cluster!
    goto ERROR_SECTION
)

echo.
echo [2] Cluster Info:
echo ----------------------------------------
redis-cli -c -p 7001 cluster info 2>nul

echo.
echo [3] Node Health Check:
echo ----------------------------------------
for %%p in (7001 7002 7003 8001 8002 8003) do (
    redis-cli -p %%p ping >nul 2>&1
    if !errorlevel! equ 0 (
        echo Node %%p: [ONLINE]
    ) else (
        echo Node %%p: [OFFLINE] ^^^!^^^!^^^!
    )
)

echo.
echo [4] Memory Usage:
echo ----------------------------------------
for %%p in (7001 7002 7003) do (
    echo Master %%p:
    redis-cli -p %%p info memory | findstr "used_memory_human\|used_memory_peak_human" 2>nul
    echo.
)

echo.
echo ========================================
echo Press Ctrl+C to exit, or wait 10 seconds for refresh...
timeout /t 10 /nobreak >nul
goto MONITOR_LOOP

:ERROR_SECTION
echo.
echo ========================================
echo ERROR: Redis cluster is not accessible!
echo Please check if all nodes are running.
echo ========================================
pause
exit /b 1
