// 游戏配置
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const BLOCK_SIZE = 30;

// 游戏状态
let board = [];
let currentPiece = null;
let nextPiece = null;
let score = 0;
let level = 1;
let lines = 0;
let gameRunning = false;
let isPaused = false;
let dropTime = 0;
let dropInterval = 1000;

// Canvas 元素
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const nextCanvas = document.getElementById('nextCanvas');
const nextCtx = nextCanvas.getContext('2d');

// 俄罗斯方块形状定义
const PIECES = [
    {
        shape: [
            [1, 1, 1, 1]
        ],
        color: '#00f0f0'
    },
    {
        shape: [
            [1, 1],
            [1, 1]
        ],
        color: '#f0f000'
    },
    {
        shape: [
            [0, 1, 0],
            [1, 1, 1]
        ],
        color: '#a000f0'
    },
    {
        shape: [
            [0, 1, 1],
            [1, 1, 0]
        ],
        color: '#00f000'
    },
    {
        shape: [
            [1, 1, 0],
            [0, 1, 1]
        ],
        color: '#f00000'
    },
    {
        shape: [
            [1, 0, 0],
            [1, 1, 1]
        ],
        color: '#f0a000'
    },
    {
        shape: [
            [0, 0, 1],
            [1, 1, 1]
        ],
        color: '#0000f0'
    }
];

// 初始化游戏板
function initBoard() {
    board = [];
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        board[y] = [];
        for (let x = 0; x < BOARD_WIDTH; x++) {
            board[y][x] = 0;
        }
    }
}

// 创建新方块
function createPiece() {
    const pieceType = Math.floor(Math.random() * PIECES.length);
    const piece = JSON.parse(JSON.stringify(PIECES[pieceType]));
    piece.x = Math.floor(BOARD_WIDTH / 2) - Math.floor(piece.shape[0].length / 2);
    piece.y = 0;
    return piece;
}

// 绘制方块
function drawBlock(ctx, x, y, color) {
    ctx.fillStyle = color;
    ctx.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.strokeRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
}

// 绘制游戏板
function drawBoard() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制已放置的方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (board[y][x]) {
                drawBlock(ctx, x, y, board[y][x]);
            }
        }
    }
    
    // 绘制当前方块
    if (currentPiece) {
        drawPiece(ctx, currentPiece);
    }
}

// 绘制方块
function drawPiece(ctx, piece) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                drawBlock(ctx, piece.x + x, piece.y + y, piece.color);
            }
        }
    }
}

// 绘制下一个方块
function drawNextPiece() {
    nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height);
    if (nextPiece) {
        const offsetX = (nextCanvas.width / BLOCK_SIZE - nextPiece.shape[0].length) / 2;
        const offsetY = (nextCanvas.height / BLOCK_SIZE - nextPiece.shape.length) / 2;
        
        for (let y = 0; y < nextPiece.shape.length; y++) {
            for (let x = 0; x < nextPiece.shape[y].length; x++) {
                if (nextPiece.shape[y][x]) {
                    nextCtx.fillStyle = nextPiece.color;
                    nextCtx.fillRect(
                        (offsetX + x) * BLOCK_SIZE,
                        (offsetY + y) * BLOCK_SIZE,
                        BLOCK_SIZE,
                        BLOCK_SIZE
                    );
                    nextCtx.strokeStyle = '#333';
                    nextCtx.strokeRect(
                        (offsetX + x) * BLOCK_SIZE,
                        (offsetY + y) * BLOCK_SIZE,
                        BLOCK_SIZE,
                        BLOCK_SIZE
                    );
                }
            }
        }
    }
}

// 检查碰撞
function isValidMove(piece, dx, dy, newShape) {
    const shape = newShape || piece.shape;
    const newX = piece.x + dx;
    const newY = piece.y + dy;
    
    for (let y = 0; y < shape.length; y++) {
        for (let x = 0; x < shape[y].length; x++) {
            if (shape[y][x]) {
                const boardX = newX + x;
                const boardY = newY + y;
                
                if (boardX < 0 || boardX >= BOARD_WIDTH || 
                    boardY >= BOARD_HEIGHT || 
                    (boardY >= 0 && board[boardY][boardX])) {
                    return false;
                }
            }
        }
    }
    return true;
}

// 旋转方块
function rotatePiece(piece) {
    const rotated = [];
    const rows = piece.shape.length;
    const cols = piece.shape[0].length;

    for (let x = 0; x < cols; x++) {
        rotated[x] = [];
        for (let y = rows - 1; y >= 0; y--) {
            rotated[x][rows - 1 - y] = piece.shape[y][x];
        }
    }

    return rotated;
}

// 放置方块到游戏板
function placePiece() {
    for (let y = 0; y < currentPiece.shape.length; y++) {
        for (let x = 0; x < currentPiece.shape[y].length; x++) {
            if (currentPiece.shape[y][x]) {
                const boardY = currentPiece.y + y;
                const boardX = currentPiece.x + x;
                if (boardY >= 0) {
                    board[boardY][boardX] = currentPiece.color;
                }
            }
        }
    }
}

// 清除完整的行
function clearLines() {
    let linesCleared = 0;

    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
        let isFullLine = true;
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (!board[y][x]) {
                isFullLine = false;
                break;
            }
        }

        if (isFullLine) {
            board.splice(y, 1);
            board.unshift(new Array(BOARD_WIDTH).fill(0));
            linesCleared++;
            y++; // 重新检查当前行
        }
    }

    if (linesCleared > 0) {
        lines += linesCleared;
        score += linesCleared * 100 * level;
        level = Math.floor(lines / 10) + 1;
        dropInterval = Math.max(50, 1000 - (level - 1) * 50);
        updateScore();
    }
}

// 更新分数显示
function updateScore() {
    document.getElementById('score').textContent = score;
    document.getElementById('level').textContent = level;
    document.getElementById('lines').textContent = lines;
}

// 游戏结束检查
function isGameOver() {
    return !isValidMove(currentPiece, 0, 0);
}

// 移动方块
function movePiece(dx, dy) {
    if (isValidMove(currentPiece, dx, dy)) {
        currentPiece.x += dx;
        currentPiece.y += dy;
        return true;
    }
    return false;
}

// 旋转当前方块
function rotateCurrent() {
    const rotated = rotatePiece(currentPiece);
    if (isValidMove(currentPiece, 0, 0, rotated)) {
        currentPiece.shape = rotated;
    }
}

// 生成新方块
function spawnNewPiece() {
    currentPiece = nextPiece || createPiece();
    nextPiece = createPiece();

    if (isGameOver()) {
        gameOver();
        return;
    }

    drawNextPiece();
}

// 游戏结束
function gameOver() {
    gameRunning = false;
    document.getElementById('finalScore').textContent = score;
    document.getElementById('gameOver').style.display = 'block';
}

// 重新开始游戏
function restartGame() {
    initBoard();
    score = 0;
    level = 1;
    lines = 0;
    dropTime = 0;
    dropInterval = 1000;
    gameRunning = true;
    isPaused = false;

    currentPiece = null;
    nextPiece = null;

    spawnNewPiece();
    updateScore();

    document.getElementById('gameOver').style.display = 'none';

    if (!gameRunning) {
        gameLoop();
    }
}

// 游戏主循环
function gameLoop(timestamp) {
    if (!gameRunning || isPaused) {
        if (gameRunning) {
            requestAnimationFrame(gameLoop);
        }
        return;
    }

    if (timestamp - dropTime > dropInterval) {
        if (!movePiece(0, 1)) {
            placePiece();
            clearLines();
            spawnNewPiece();
        }
        dropTime = timestamp;
    }

    drawBoard();
    requestAnimationFrame(gameLoop);
}

// 键盘控制
document.addEventListener('keydown', (event) => {
    if (!gameRunning || isPaused) return;

    switch (event.code) {
        case 'ArrowLeft':
            movePiece(-1, 0);
            break;
        case 'ArrowRight':
            movePiece(1, 0);
            break;
        case 'ArrowDown':
            if (movePiece(0, 1)) {
                score += 1;
                updateScore();
            }
            break;
        case 'ArrowUp':
            rotateCurrent();
            break;
        case 'Space':
            isPaused = !isPaused;
            event.preventDefault();
            break;
    }

    if (!isPaused) {
        drawBoard();
    }
});

// 初始化游戏
function initGame() {
    initBoard();
    updateScore();
    spawnNewPiece();
    gameRunning = true;
    requestAnimationFrame(gameLoop);
}

// 页面加载完成后启动游戏
window.addEventListener('load', initGame);
