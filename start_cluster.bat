@echo off
echo Starting Redis Cluster nodes...

echo Starting Redis node 7001...
start "Redis-7001" /D "c:\redis\redis-cluster\7001" redis-server.exe redis.windows.conf

echo Starting Redis node 7002...
start "Redis-7002" /D "c:\redis\redis-cluster\7002" redis-server.exe redis.windows.conf

echo Starting Redis node 7003...
start "Redis-7003" /D "c:\redis\redis-cluster\7003" redis-server.exe redis.windows.conf

echo Starting Redis node 8001...
start "Redis-8001" /D "c:\redis\redis-cluster\8001" redis-server.exe redis.windows.conf

echo Starting Redis node 8002...
start "Redis-8002" /D "c:\redis\redis-cluster\8002" redis-server.exe redis.windows.conf

echo Starting Redis node 8003...
start "Redis-8003" /D "c:\redis\redis-cluster\8003" redis-server.exe redis.windows.conf

echo Waiting for nodes to start...
timeout /t 5

echo Checking if all nodes are running...
netstat -an | findstr "7001 7002 7003 8001 8002 8003"

echo.
echo All Redis nodes should now be running.
echo You can now create the cluster with:
echo redis-cli.exe --cluster create --cluster-replicas 1 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 127.0.0.1:8001 127.0.0.1:8002 127.0.0.1:8003
pause
