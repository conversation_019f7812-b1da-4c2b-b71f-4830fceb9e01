# Redis Cluster Configuration Template
# Replace PORT with actual port number (7001, 7002, etc.)

port PORT
bind 127.0.0.1
protected-mode no

# Cluster Configuration
cluster-enabled yes
cluster-config-file nodes-PORT.conf
cluster-node-timeout 15000
cluster-announce-ip 127.0.0.1
cluster-announce-port PORT
cluster-announce-bus-port 1PORT

# Logging
loglevel notice
logfile "server_log.txt"

# Persistence
save 900 1
save 300 10
save 60 10000
dbfilename dump-PORT.rdb

# Memory Management
maxmemory-policy allkeys-lru

# Network
tcp-keepalive 300
timeout 0

# Other Settings
daemonize no
supervised no
